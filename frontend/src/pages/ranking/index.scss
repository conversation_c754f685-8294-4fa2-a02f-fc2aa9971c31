.page-ranking {
  min-height: 100vh;
  background-color: #f3f4f6;
  padding-bottom: 120px;
  display: flex;
  flex-direction: column;
}

.header {
  position: sticky;
  top: 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(8px);
  z-index: 10;
  padding: 32rpx;
  padding-top: 32rpx;
  padding-bottom: 16rpx;

  .tab-switch {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #e5e7eb;
    border-radius: 9999px;
    padding: 8rpx;
    margin-bottom: 32rpx;

    .tab-item {
      flex: 1;
      padding: 16rpx 48rpx;
      text-align: center;
      border-radius: 9999px;
      transition: all 0.3s ease;

      .tab-text {
        font-size: 28rpx;
        font-weight: 600;
        color: #6b7280;
      }

      &.tab-active {
        background-color: #4f46e5;
        box-shadow: 0 8rpx 12rpx -2rpx rgba(0, 0, 0, 0.1);

        .tab-text {
          color: white;
        }
      }
    }
  }
}

.main-content {
  flex: 1;
  padding: 24rpx;
  padding-top: 32rpx;
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

.ranking-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;

  .ranking-title {
    display: flex;
    align-items: center;

    .title-icon {
      font-size: 36rpx;
      margin-right: 16rpx;
      color: #a855f7;

      &.material-icons {
        font-family: "Material Icons";
        font-weight: normal;
        font-style: normal;
        line-height: 1;
        letter-spacing: normal;
        text-transform: none;
        display: inline-block;
        white-space: nowrap;
        word-wrap: normal;
        direction: ltr;
        font-feature-settings: "liga";
        -webkit-font-feature-settings: "liga";
        -webkit-font-smoothing: antialiased;
      }
    }

    .title-text {
      font-size: 36rpx;
      font-weight: bold;
      color: #1f2937;
    }
  }

  .update-time {
    font-size: 24rpx;
    color: #9ca3af;
  }
}

.ranking-list {
  width: 100%;

  .ranking-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 24rpx;
    background-color: #ffffff;
    border-radius: 20rpx;
    padding: 20rpx;
    box-shadow: 0 4rpx 12rpx 0 rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    width: 100%;
    box-sizing: border-box;
    overflow: hidden;

    &:hover {
      transform: translateY(-4rpx);
      box-shadow: 0 8rpx 24rpx 0 rgba(0, 0, 0, 0.1);
    }

    .drama-cover {
      position: relative;
      flex-shrink: 0;
      width: 140rpx;
      height: 186rpx;
      margin-right: 20rpx;

      .cover-placeholder {
        position: absolute;
        inset: 0;
        background-color: #d1d5db;
        border-radius: 16rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        .cover-text {
          color: #6b7280;
          font-size: 28rpx;
        }
      }

      .ranking-badge {
        position: absolute;
        top: 0;
        left: 0;
        padding: 4rpx 12rpx;
        border-radius: 16rpx 0 16rpx 0;

        .ranking-number {
          font-size: 24rpx;
          font-weight: bold;
          color: white;
        }

        &.badge-first {
          background-color: #dc2626;
        }

        &.badge-second {
          background-color: #f59e0b;
        }

        &.badge-third {
          background-color: #eab308;
        }

        &.badge-other {
          background-color: #6b7280;
        }
      }
    }

    .drama-info {
      flex: 1;
      min-width: 0; // 确保flex子元素可以收缩
      overflow: hidden; // 防止内容溢出

      .drama-title {
        font-size: 30rpx;
        font-weight: bold;
        color: #1f2937;
        margin-bottom: 12rpx;
        display: block;
        line-height: 1.4;
        word-wrap: break-word;
        overflow-wrap: break-word;
      }

      .drama-tags {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 16rpx;
        margin-bottom: 16rpx;

        .drama-tag {
          background-color: #e5e7eb;
          color: #6b7280;
          padding: 4rpx 16rpx;
          border-radius: 9999px;
          font-size: 24rpx;
          white-space: nowrap;
        }
      }

      .drama-reason {
        padding-top: 8rpx;
        display: flex;
        flex-wrap: wrap;
        align-items: flex-start;
        line-height: 1.5;

        .reason-label {
          font-weight: 600;
          color: #f59e0b;
          font-size: 26rpx;
          flex-shrink: 0;
        }

        .reason-text {
          color: #6b7280;
          font-size: 26rpx;
          margin-left: 8rpx;
          flex: 1;
          word-wrap: break-word;
          overflow-wrap: break-word;
          line-height: 1.5;
        }
      }
    }
  }
}

// 标签区块样式
.tag-sections {
  margin-top: 48rpx;
  width: 100%;
}

.tag-section {
  margin-bottom: 48rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 12rpx 0 rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.tag-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.tag-section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #1f2937;
}

.tag-section-more {
  font-size: 32rpx;
  color: #9ca3af;
  font-family: "Material Icons";
  font-weight: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  font-feature-settings: "liga";
  -webkit-font-feature-settings: "liga";
  -webkit-font-smoothing: antialiased;
}

.tag-section-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  width: 100%;
}

.tag-section-item {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  cursor: pointer;
  transition: transform 0.3s ease;
  width: 100%;
  min-width: 0;

  &:hover {
    transform: scale(1.02);
  }
}

.tag-section-cover {
  position: relative;
  width: 100%;
  aspect-ratio: 3/4;
  background-color: #e5e7eb;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.tag-section-tag {
  position: absolute;
  top: 16rpx;
  left: 16rpx;
  color: white;
  font-size: 20rpx;
  font-weight: bold;
  padding: 4rpx 16rpx;
  border-radius: 12rpx;

  &.tag-red {
    background-color: #ef4444;
  }

  &.tag-green {
    background-color: #22c55e;
  }

  &.tag-purple {
    background-color: #a855f7;
  }

  &.tag-blue {
    background-color: #3b82f6;
  }

  &.tag-yellow {
    background-color: #eab308;
  }

  &.tag-gray {
    background-color: #6b7280;
  }

  &.tag-orange {
    background-color: #f97316;
  }

  &.tag-pink {
    background-color: #ec4899;
  }

  &.tag-dark {
    background-color: #374151;
  }

  &.tag-black {
    background-color: #1f2937;
  }

  &.tag-cyan {
    background-color: #06b6d4;
  }
}

.tag-section-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #1f2937;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}

.tag-section-info {
  font-size: 24rpx;
  color: #6b7280;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 加载更多样式
.loading-more {
  text-align: center;
  padding: 32rpx;
  margin-top: 32rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #9ca3af;
}

// 响应式设计
@media (max-width: 375px) {
  .main-content {
    padding: 16rpx !important;
  }

  .ranking-item {
    padding: 16rpx !important;
    margin-bottom: 20rpx !important;

    .drama-cover {
      width: 120rpx !important;
      height: 160rpx !important;
      margin-right: 16rpx !important;
    }

    .drama-info {
      .drama-title {
        font-size: 26rpx !important;
        margin-bottom: 8rpx !important;
      }

      .drama-tags {
        gap: 8rpx !important;
        margin-bottom: 10rpx !important;

        .drama-tag {
          font-size: 20rpx !important;
          padding: 2rpx 10rpx !important;
        }
      }

      .drama-reason {
        .reason-label,
        .reason-text {
          font-size: 22rpx !important;
        }
      }
    }
  }

  .tag-section {
    padding: 16rpx !important;
    margin-bottom: 32rpx !important;
  }

  .tag-section-grid {
    gap: 16rpx !important;
  }

  .tag-section-name {
    font-size: 24rpx !important;
  }

  .tag-section-info {
    font-size: 20rpx !important;
  }
}
